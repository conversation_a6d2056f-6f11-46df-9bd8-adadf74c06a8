{% extends 'base.html' %}

{% block title %}CVs - HR Management System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>
        <i class="fas fa-file-alt me-2"></i>CVs
    </h1>
    <a href="{{ url_for('upload_cv') }}" class="btn btn-success">
        <i class="fas fa-upload me-1"></i> Upload CV
    </a>
</div>

<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">Filter by Job</h5>
            </div>
            <div class="card-body">
                <form method="GET" action="{{ url_for('cvs') }}" class="d-flex">
                    <select name="job" class="form-select me-2" aria-label="Select Job">
                        <option value="">All Jobs</option>
                        {% for job in jobs %}
                            <option value="{{ job.title }}" {% if selected_job == job.title %}selected{% endif %}>
                                {{ job.title }}
                            </option>
                        {% endfor %}
                    </select>
                    <button type="submit" class="btn btn-primary">Filter</button>
                </form>
            </div>
        </div>
    </div>
</div>

{% if selected_job %}
    <h2 class="mb-3">CVs for "{{ selected_job }}"</h2>
    
    {% if cvs %}
        <div class="table-responsive" style="overflow: visible;">
            <table class="table table-striped table-hover">
                <thead class="table-dark">
                    <tr>
                        <th>Filename</th>
                        <th>Candidate</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for cv in cvs %}
                    <tr>
                        <td>{{ cv.filename }}</td>
                        <td>{{ cv.candidate_name }}</td>
                        <td>
                            {% set status = cv.status or 'received' %}
                            {% if status == 'received' %}
                                <span class="badge bg-info">Received</span>
                            {% elif status == 'under_review' %}
                                <span class="badge bg-warning">Under Review</span>
                            {% elif status == 'interview_scheduled' %}
                                <span class="badge bg-primary">Interview Scheduled</span>
                            {% elif status == 'rejected' %}
                                <span class="badge bg-danger">Rejected</span>
                            {% elif status == 'hired' %}
                                <span class="badge bg-success">Hired</span>
                            {% else %}
                                <span class="badge bg-secondary">{{ status|title }}</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="{{ url_for('view_cv', cv_id=cv.id) }}" target="_blank" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-eye"></i> View
                                </a>
                                <button class="btn btn-sm btn-outline-secondary" type="button" onclick="showStatusModal({{ cv.id }}, '{{ cv.status or 'received' }}')">
                                    <i class="fas fa-edit"></i> Status
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    {% else %}
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>No CVs found for this job.
        </div>
    {% endif %}
{% else %}
    <div class="alert alert-info">
        <i class="fas fa-info-circle me-2"></i> Please select a job from the filter to view its CVs.
    </div>
{% endif %}

<!-- Status Update Modal -->
<div class="modal fade" id="statusModal" tabindex="-1" aria-labelledby="statusModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="statusModalLabel">
                    <i class="fas fa-edit me-2"></i>Update Applicant Status
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Select the new status for this applicant:</p>
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-outline-info" onclick="updateStatus('received')">
                        <i class="fas fa-inbox me-2"></i>Received
                        <small class="d-block text-muted">Initial status when CV is uploaded</small>
                    </button>
                    <button type="button" class="btn btn-outline-warning" onclick="updateStatus('under_review')">
                        <i class="fas fa-search me-2"></i>Under Review
                        <small class="d-block text-muted">CV is being evaluated</small>
                    </button>
                    <button type="button" class="btn btn-outline-primary" onclick="updateStatus('interview_scheduled')">
                        <i class="fas fa-calendar me-2"></i>Interview Scheduled
                        <small class="d-block text-muted">Candidate invited for interview</small>
                    </button>
                    <button type="button" class="btn btn-outline-danger" onclick="updateStatus('rejected')">
                        <i class="fas fa-times me-2"></i>Rejected
                        <small class="d-block text-muted">Application declined</small>
                    </button>
                    <button type="button" class="btn btn-outline-success" onclick="updateStatus('hired')">
                        <i class="fas fa-check me-2"></i>Hired
                        <small class="d-block text-muted">Candidate was hired</small>
                    </button>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
let currentCvId = null;

function showStatusModal(cvId, currentStatus) {
    currentCvId = cvId;

    // Highlight current status
    const modal = document.getElementById('statusModal');
    const buttons = modal.querySelectorAll('.btn-outline-info, .btn-outline-warning, .btn-outline-primary, .btn-outline-danger, .btn-outline-success');

    buttons.forEach(btn => {
        btn.classList.remove('active');
        const statusValue = btn.getAttribute('onclick').match(/'([^']+)'/)[1];
        if (statusValue === currentStatus) {
            btn.classList.add('active');
        }
    });

    new bootstrap.Modal(modal).show();
}

function updateStatus(newStatus) {
    if (currentCvId && confirm('Are you sure you want to update the status of this CV?')) {
        // Create a form and submit it
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/update-cv-status/${currentCvId}`;

        const statusInput = document.createElement('input');
        statusInput.type = 'hidden';
        statusInput.name = 'status';
        statusInput.value = newStatus;

        form.appendChild(statusInput);
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
{% endblock %}
