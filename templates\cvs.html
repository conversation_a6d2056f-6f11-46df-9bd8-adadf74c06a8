{% extends 'base.html' %}

{% block title %}CVs - HR Management System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>
        <i class="fas fa-file-alt me-2"></i>CVs
    </h1>
    <a href="{{ url_for('upload_cv') }}" class="btn btn-success">
        <i class="fas fa-upload me-1"></i> Upload CV
    </a>
</div>

<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">Filter by Job</h5>
            </div>
            <div class="card-body">
                <form method="GET" action="{{ url_for('cvs') }}" class="d-flex">
                    <select name="job" class="form-select me-2" aria-label="Select Job">
                        <option value="">All Jobs</option>
                        {% for job in jobs %}
                            <option value="{{ job.title }}" {% if selected_job == job.title %}selected{% endif %}>
                                {{ job.title }}
                            </option>
                        {% endfor %}
                    </select>
                    <button type="submit" class="btn btn-primary">Filter</button>
                </form>
            </div>
        </div>
    </div>
</div>

{% if selected_job %}
    <h2 class="mb-3">CVs for "{{ selected_job }}"</h2>
    
    {% if cvs %}
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead class="table-dark">
                    <tr>
                        <th>Filename</th>
                        <th>Candidate</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for cv in cvs %}
                    <tr>
                        <td>{{ cv.filename }}</td>
                        <td>{{ cv.candidate_name }}</td>
                        <td>
                            <!-- Actions here -->
                            <a href="{{ url_for('view_cv', cv_id=cv.id) }}" target="_blank" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-eye"></i> View
                            </a>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    {% else %}
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>No CVs found for this job.
        </div>
    {% endif %}
{% else %}
    <div class="alert alert-info">
        <i class="fas fa-info-circle me-2"></i> Please select a job from the filter to view its CVs.
    </div>
{% endif %}
{% endblock %}
